package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"time"
)

const (
	BaseURL = "https://ai.gitee.com/v1"
	APIKey  = "IWRMVNQNCNODUXPKDL3I4JGKBRL0GJURNK3RCM1I"
	Model   = "whisper-large-v3-turbo"
)

// WhisperRequest represents the request structure for Whisper API
type WhisperRequest struct {
	Model    string `json:"model"`
	Language string `json:"language,omitempty"`
}

// WhisperResponse represents the response structure from Whisper API
type WhisperResponse struct {
	Text string `json:"text"`
}

// WhisperClient handles communication with the Whisper API
type WhisperClient struct {
	BaseURL string
	APIKey  string
	Client  *http.Client
}

// NewWhisperClient creates a new Whisper client
func NewWhisperClient(baseURL, apiKey string) *WhisperClient {
	return &WhisperClient{
		BaseURL: baseURL,
		APIKey:  apiKey,
		Client: &http.Client{
			Timeout: 60 * time.Second,
		},
	}
}

// TranscribeAudio transcribes audio file using Whisper API
func (w *WhisperClient) TranscribeAudio(audioFilePath string) (*WhisperResponse, error) {
	// Check if file exists
	if _, err := os.Stat(audioFilePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("audio file does not exist: %s", audioFilePath)
	}

	// Open the audio file
	file, err := os.Open(audioFilePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open audio file: %v", err)
	}
	defer file.Close()

	// Create a buffer to store the multipart form data
	var requestBody bytes.Buffer
	writer := multipart.NewWriter(&requestBody)

	// Add the audio file to the form
	part, err := writer.CreateFormFile("file", filepath.Base(audioFilePath))
	if err != nil {
		return nil, fmt.Errorf("failed to create form file: %v", err)
	}

	_, err = io.Copy(part, file)
	if err != nil {
		return nil, fmt.Errorf("failed to copy file content: %v", err)
	}

	// Add model parameter
	err = writer.WriteField("model", Model)
	if err != nil {
		return nil, fmt.Errorf("failed to write model field: %v", err)
	}

	// Add language parameter (optional, let Whisper auto-detect)
	// err = writer.WriteField("language", "zh")
	// if err != nil {
	//     return nil, fmt.Errorf("failed to write language field: %v", err)
	// }

	// Close the writer to finalize the form
	err = writer.Close()
	if err != nil {
		return nil, fmt.Errorf("failed to close multipart writer: %v", err)
	}

	// Create the HTTP request
	url := fmt.Sprintf("%s/audio/transcriptions", w.BaseURL)
	req, err := http.NewRequest("POST", url, &requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %v", err)
	}

	// Set headers
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", w.APIKey))
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// Send the request
	fmt.Printf("Sending request to: %s\n", url)
	fmt.Printf("File: %s\n", audioFilePath)
	fmt.Println("Processing audio file...")

	resp, err := w.Client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send HTTP request: %v", err)
	}
	defer resp.Body.Close()

	// Read the response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}

	// Check for HTTP errors
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Parse the JSON response
	var whisperResp WhisperResponse
	err = json.Unmarshal(body, &whisperResp)
	if err != nil {
		return nil, fmt.Errorf("failed to parse JSON response: %v", err)
	}

	return &whisperResp, nil
}

func main() {
	// Check command line arguments
	if len(os.Args) != 2 {
		fmt.Fprintf(os.Stderr, "Usage: %s <audio_file_path>\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Example: go run whisper_asr.go ./test.wav\n")
		os.Exit(1)
	}

	audioFilePath := os.Args[1]

	// Create Whisper client
	client := NewWhisperClient(BaseURL, APIKey)

	// Transcribe the audio
	result, err := client.TranscribeAudio(audioFilePath)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error transcribing audio: %v\n", err)
		os.Exit(1)
	}

	// Print the result
	fmt.Println("\n=== Transcription Result ===")
	fmt.Println(result.Text)
	fmt.Println("============================")
}
